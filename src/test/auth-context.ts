/* global testContext */
/* eslint-env mocha */

import { expect } from 'chai';
import path from 'path';
import { fileURLToPath } from 'url';
import type { Application } from 'express';
import { BASApiClient } from './utils/apiclient.ts';
import * as jwt from '../connectors/lib/jwt.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

suite('Auth Context', function () {
    let app: Application;
    let bas: BASApiClient;

    const kind = 'cloud';
    const userInfo = {
        userId: 'test-user',
        name: 'Test User',
        displayName: 'Test User',
        avatarURL: 'https://placehold.it/60x60',
    };

    const projectId = 1;
    const siteId = 1000;
    const jwtsecret = 'foobar';

    const getPlatformToken = (extra: unknown) => {
        const { siteId, projectId, role } = extra as any;
        return jwt.encode(
            {
                exp: Date.now() + 3600,
                sub: 'create_archive',
                iss: 'cloud-server',
                siteId,
                projectId,
                role,
            },
            jwtsecret,
            'HS256'
        );
    };

    const platformTokenExtra = {
        siteId: siteId,
        projectId: projectId,
        role: 'ADMIN',
    };

    async function createTestProject() {
        const createRes = await bas.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
        let openRes = await bas.open();
        const { token } = openRes.body;
        return token;
    }

    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: projectId,
            siteID: siteId,
            getPlatformToken,
            platformTokenExtra,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
        });
    });

    test('sessionData and platformData are accessible via req.bas.auth', async function () {
        // Create a test project to get a valid token
        const token = await createTestProject();

        // Make a request that goes through the auth pipeline
        const response = await bas.getTOC({ token, branchID: 'All' });
        expect(response.status).to.equal(200);

        // The fact that the request succeeded means the auth context is working
        // since the middleware sets req.bas.auth.sessionData and req.bas.auth.platformData
        // and the handlers use them successfully
    });

    test('auth context maintains backward compatibility', async function () {
        // Create a test project
        const token = await createTestProject();

        // Test that APIs that use the new auth context still work
        const flushResponse = await bas.flush({ token, force: false });
        expect(flushResponse.status).to.equal(200);

        const closeResponse = await bas.close({ token });
        expect(closeResponse.status).to.equal(200);
    });

    test('auth context works with platform data', async function () {
        // Create a test project
        const token = await createTestProject();

        // Make a request that would use platform data
        const response = await bas.flush({ token, force: false });
        expect(response.status).to.equal(200);

        // If platform data wasn't properly accessible, this would fail
        expect(response.body).to.not.have.property('error');
    });
});
