import * as uuid from 'uuid';
import { MySQLDriver, MySQLDriverPool } from './mysql-driver.ts';
import { type BASLegacyErrorObject, type BASLegacyCallback, callWithLegacyCallback } from './calling-style.ts';
import barMod from '@balsamiq/bmpr/lib/BalsamiqArchive.js';
import { Metrics } from './metrics.ts';
import type { Logger } from '@balsamiq/logging';
import type { ConfigLoaderResult } from './configLoader.ts';
import { RedisAdapter } from './redisAdapter.ts';
import type { PoolConnection, MysqlError } from 'mysql';
import { DBConnector, DBConnectorPool } from './database.ts';

interface PendingSession {
    action: string;
    pendingSince: number;
    start: number;
    token?: string;
    archiveID?: string;
}

interface PoolMetrics {
    openedSession: number;
    activePoolConnection: number;
    connectionLimit: number;
}

interface SessionStats extends PoolMetrics {
    pendingSession: PendingSession[];
}

interface HistoryOfPendingSessionsItem {
    ts: number;
    numPendingSessions: number;
    newPendingSessionsPerMin: number;
    avgNewPendingSessionsPerMin?: number;
}

export interface HistoryOfPendingSessionsCount {
    historyOfPendingSessionsCount: HistoryOfPendingSessionsItem[];
}

export interface SessionData {
    archiveID?: string;
    token?: string;
    sessionUUID: string;
    connection: PoolConnection; // mysql driver connection
    metricTimer: ReturnType<InstanceType<typeof Metrics>['trackTimeInterval']>;
    sessionManager: SessionManager;
    action: string;
    rootAction: string;
    start: number;
    logger: Logger;
    dbConnector: DBConnector;
    error?: string | Error; // Updated to accept both string and Error types
    user?: {
        USERINFO?: string;
        PERMISSIONS?: string;
        ARCHIVE_ID?: string;
        USERNAME?: string;
        INTERNAL_ID?: string;
        PLATFORM_TOKEN?: string;
        TOKEN?: string;
    };
}

interface RunningDerivativeParams {
    now: number;
    numPendingSessions: number;
    state: HistoryOfPendingSessionsCount | null;
    suicide: (avgNewPendingSessionsPerMin: number) => void;
    minimumHistoryInSec: number;
    historyDurationInSec: number;
    ratePerMinThreshold: number;
}

// Constants
const barLOCK: boolean = true;
const PENDING_SESSION_DELTA: number = 15000; // pending session threshold 15 secs
const HISTORY_OF_PENDING_SESSIONS_DURATION_IN_SEC: number = 60 * 2; // 2 minutes
const MINIMUM_HISTORY_OF_PENDING_SESSIONS_DURATION_IN_SEC: number = 60; // 1 minute
const NEW_PENDING_SESSION_RATE_PER_MIN_THRESHOLD: number = 100; // suicide if more than 100 new pending sessions per minute

export class SessionManager {
    private sessionDataStore: Record<string, SessionData>;
    private connectorLockType: null | 'READ';
    private mySqlDriverInstance: MySQLDriverPool; // We'll keep this as any until mysql_driver.js is converted
    private metrics: Metrics;
    private logger: Logger;
    private configInfo: ConfigLoaderResult;
    private redisAdapter: RedisAdapter;
    private basDBName: string;
    private dbConnectorPool: DBConnectorPool;
    private barMod: typeof barMod;
    public isReady: Promise<void>;
    private setReady!: () => void;

    /**
     * Creates an instance of SessionManager.
     *
     * @param connectorLockType - The type of connector lock.
     * @param mySqlDriverInstance - The MySQL driver instance.
     * @param metrics - The metrics instance.
     * @param configInfo - The configuration information.
     * @param redisAdapter - The Redis adapter instance.
     * @param logger - The logger instance.
     */
    constructor(
        connectorLockType: null | 'READ',
        mySqlDriverInstance: MySQLDriverPool,
        metrics: Metrics,
        configInfo: ConfigLoaderResult,
        redisAdapter: RedisAdapter,
        logger: Logger
    ) {
        this.sessionDataStore = {};
        this.connectorLockType = connectorLockType;
        this.mySqlDriverInstance = mySqlDriverInstance;
        this.metrics = metrics;
        this.logger = logger.getLogger({ module: 'sessionManager' });
        this.configInfo = configInfo;
        this.redisAdapter = redisAdapter;
        this.basDBName = configInfo.config.mySQLConfig.basDBName;
        this.barMod = barMod;

        this.isReady = new Promise((resolve) => {
            this.setReady = resolve;
        });

        this.dbConnectorPool = new DBConnectorPool({
            mySqlDriverInstance: mySqlDriverInstance,
            logger: logger,
            config: configInfo.config,
            redisAdapter: this.redisAdapter,
            callback: () => {
                if (this.setReady) {
                    this.setReady();
                }
            },
        });
    }

    /**
     * Executes a function with a session and automatically releases it afterwards.
     *
     * @template T The return type of the function
     * @param logger The logger instance
     * @param path The action path
     * @param func The function to execute with the session
     * @returns The result of the function
     */
    async withSession<T>(logger: Logger, path: string, func: (dbSession: SessionData) => T | Promise<T>): Promise<T> {
        const dbSession = await callWithLegacyCallback<SessionData>((cb) => this.createSession(logger, path, cb));
        try {
            return await func(dbSession);
        } finally {
            try {
                await callWithLegacyCallback<{}>((cb) => dbSession.sessionManager.releaseSession(dbSession, cb));
            } catch (err) {
                logger.error('Error releasing session', err as Error);
            }
        }
    }

    /**
     * Creates a session.
     *
     * @param logger - The logger instance.
     * @param action - The action being performed.
     * @param callback - The callback function.
     * @returns void
     */
    createSession(logger: Logger, action: string, callback: BASLegacyCallback<SessionData>): void {
        this.mySqlDriverInstance.getConnection((obj) => {
            if ('error' in obj) {
                callback(obj);
            } else {
                const connection = obj;
                const sessionUUID = uuid.v1();
                const rootAction = action || 'unknown path';
                const sessionData: SessionData = {
                    sessionUUID,
                    connection,
                    metricTimer: this.metrics.trackTimeInterval('session-duration ' + rootAction),
                    sessionManager: this,
                    action: rootAction,
                    rootAction,
                    start: new Date().getTime(),
                    logger,
                    dbConnector: {} as DBConnector, // Placeholder, will be set later
                };

                this.dbConnectorPool.getDBConnector(logger, sessionData, this.connectorLockType, (obj) => {
                    if ('error' in obj) {
                        callback(obj);
                    } else {
                        sessionData.dbConnector = obj;
                        this.sessionDataStore[sessionUUID] = sessionData;
                        callback(this.sessionDataStore[sessionUUID]);
                    }
                });
            }
        });
    }

    /**
     * Resumes an existing session with a new connection.
     *
     * @param action - The action being performed.
     * @param sessionData - The existing session data.
     * @param callback - The callback function.
     * @returns void
     */
    resumeSession(action: string, sessionData: SessionData, callback: BASLegacyCallback<DBConnector>): void {
        this.mySqlDriverInstance.getConnection((obj) => {
            if ('error' in obj) {
                callback(obj);
            } else {
                sessionData.connection = obj;
                sessionData.action = sessionData.rootAction + '::' + action;
                sessionData.start = new Date().getTime();
                sessionData.metricTimer = this.metrics.trackTimeInterval('session-duration ' + sessionData.action);

                this.dbConnectorPool.getDBConnector(sessionData.logger, sessionData, this.connectorLockType, (obj) => {
                    if ('error' in obj) {
                        callback(obj);
                    } else {
                        const dbConnector = obj;
                        this.sessionDataStore[sessionData.sessionUUID] = sessionData;
                        callback(dbConnector);
                    }
                });
            }
        });
    }

    /**
     * Releases a session.
     *
     * @param sessionData - The session data.
     * @param callback - The callback function.
     */
    releaseSession(sessionData: SessionData, callback?: BASLegacyCallback<{}>): void {
        const logger = this.logger.getLogger({ action: 'releaseSession' });
        const finalise = (callback?: BASLegacyCallback<{}>, res: BASLegacyErrorObject | {} = {}): void => {
            if (this.sessionDataStore[sessionData.sessionUUID]) {
                delete this.sessionDataStore[sessionData.sessionUUID];
            } else {
                logger.warn('unable to delete session data: ' + sessionData.sessionUUID);
            }

            this.updatePoolConnectionMetrics();
            callback && callback(res);
        };

        if (sessionData && sessionData.connection) {
            this.unlockConnection(sessionData, () => {
                // TODO: not ignore possible error on unlock
                this.mySqlDriverInstance.releaseConnection(sessionData.connection, (obj) => {
                    if (sessionData.metricTimer) {
                        sessionData.metricTimer.stop();
                    }
                    if (sessionData.dbConnector) {
                        sessionData.dbConnector.releaseSession((obj) => {
                            finalise(callback, obj);
                        });
                    } else {
                        finalise(callback, obj);
                    }
                });
            });
        } else {
            finalise(callback, {});
        }
    }

    /**
     * Unlocks tables for a session's connection.
     *
     * @param sessionData - The session data.
     * @param callback - The callback function.
     * @returns void
     */
    unlockConnection(sessionData: SessionData, callback?: BASLegacyCallback<{}>): void {
        try {
            sessionData.connection.query('UNLOCK TABLES', (err) => {
                if (err) {
                    callback && callback({ error: err });
                } else {
                    callback && callback({});
                }
            });
        } catch (e) {
            const error = e as Error;
            callback && callback({ error: 'Unexpected exception: ' + error.message });
        }
    }

    /**
     * Gets a BalsamiqArchive instance for the session.
     *
     * @param sessionData - The session data.
     * @returns The BalsamiqArchive instance.
     */
    getBar(sessionData: SessionData): BalsamiqArchive {
        // get an empty bar object with the provided connection
        const dbDriver = new MySQLDriver(sessionData.connection);
        let bar;
        let permission: number | null = null;

        let userInfo = null;
        if (sessionData.user && sessionData.user.USERINFO) {
            try {
                userInfo = JSON.parse(sessionData.user.USERINFO);
            } catch (e) {
                // Ignore parse error
            }
        }

        if (sessionData.user && sessionData.user.PERMISSIONS) {
            // in case permission is not a number we give no access role
            permission = parseInt(sessionData.user.PERMISSIONS) || 0;
        }

        bar = new this.barMod.BalsamiqArchive(dbDriver, userInfo, this.logger);
        // in case of API_OPEN, getBar is called before user session is saved: sessionData.user.PERMISSIONS is not set properly, so we cannot set the permission
        if (permission !== null && bar.setUserRole) {
            bar.setUserRole(permission);
        }

        return bar;
    }

    /**
     * Opens a BalsamiqArchive with a lock and extended options.
     *
     * @param sessionData - The session data.
     * @param archiveID - The ID of the archive to open.
     * @param lockType - The type of lock to acquire ('READ' or 'WRITE').
     * @param lockPlatformInfoToo - Whether to lock the PLATFORM_INFO table too.
     * @param callback - The callback function.
     * @returns void
     */
    openBarLockedExt(
        sessionData: SessionData,
        archiveID: string,
        lockType: 'READ' | 'WRITE',
        lockPlatformInfoToo: boolean,
        callback?: BASLegacyCallback<{ bar: BalsamiqArchive }>
    ): void {
        const bar = this.getBar(sessionData);
        const tables = bar.getTablesName();

        this.lockDatabase(sessionData, archiveID, tables, lockType, lockPlatformInfoToo, (obj) => {
            if ('error' in obj) {
                callback?.(obj);
            } else {
                bar.open(archiveID, (obj: { error: string | Error } | {}) => {
                    if ('error' in obj) {
                        callback?.(obj);
                    } else {
                        callback?.({ bar: bar });
                    }
                });
            }
        });
    }

    /**
     * Opens a BalsamiqArchive with a lock.
     *
     * @param sessionData - The session data.
     * @param archiveID - The ID of the archive to open.
     * @param lockType - The type of lock to acquire ('READ' or 'WRITE').
     * @param callback - The callback function.
     * @returns void
     */
    openBarLocked(
        sessionData: SessionData,
        archiveID: string,
        lockType: 'READ' | 'WRITE',
        callback?: BASLegacyCallback<{ bar: BalsamiqArchive }>
    ): void {
        this.openBarLockedExt(sessionData, archiveID, lockType, false, callback);
    }

    /**
     * Locks database tables for an archive.
     *
     * @param sessionData - The session data.
     * @param archiveID - The ID of the archive.
     * @param tables - The table names to lock.
     * @param lockType - The type of lock to acquire ('READ' or 'WRITE').
     * @param lockPlatformInfoToo - Whether to lock the PLATFORM_INFO table too.
     * @param callback - The callback function.
     * @returns void
     */
    lockDatabase(
        sessionData: SessionData,
        archiveID: string,
        tables: string[],
        lockType: 'READ' | 'WRITE',
        lockPlatformInfoToo: boolean,
        callback?: BASLegacyCallback<{}>
    ): void {
        let query = 'LOCK TABLES';
        const lock = lockType;

        if (barLOCK === false) {
            callback?.({});
            return;
        }

        for (let i = 0; i < tables.length; i++) {
            query += (i === 0 ? ' ' : ', ') + archiveID + '.' + tables[i] + ' ' + lock;
        }

        if (lockPlatformInfoToo) {
            query += `, ${this.basDBName}.PLATFORM_INFO` + ' ' + lock;
            // for Atlassian connectors we access the CONNECTOR_DATA in order to get the auth token
            query += `, ${this.basDBName}.CONNECTOR_DATA` + ' ' + lock;
        }

        try {
            sessionData.connection.query(query, (err) => {
                if (err) {
                    const message = err.message ? err.message : JSON.stringify(err);
                    const resp: { error: string; busy?: boolean } = { error: message };
                    if (err.code === 'ER_NO_SUCH_TABLE') {
                        resp.busy = true;
                    }
                    callback && callback(resp);
                } else {
                    callback && callback({});
                }
            });
        } catch (e) {
            const error = e as Error;
            callback && callback({ error: 'Unexpected exception: ' + error.message });
        }
    }

    /**
     * Gets metrics about the connection pool.
     *
     * @returns Pool metrics object.
     */
    getPoolMetrics(): PoolMetrics {
        const openSession = Object.keys(this.sessionDataStore);
        const activePoolConnection = this.mySqlDriverInstance.getActivePoolConnection();
        const connectionLimitForInstance = this.mySqlDriverInstance.connectionOptions.connectionLimit ?? -1;

        return {
            openedSession: openSession.length,
            activePoolConnection: activePoolConnection,
            connectionLimit: connectionLimitForInstance,
        };
    }

    /**
     * Gets detailed session statistics including pending sessions.
     *
     * @returns Session statistics object.
     */
    getSessionStat(): SessionStats {
        const openSession = Object.keys(this.sessionDataStore);
        const activePoolConnection = this.mySqlDriverInstance.getActivePoolConnection();
        const connectionLimitForInstance = this.mySqlDriverInstance.connectionOptions.connectionLimit ?? -1;
        const pendingSession: PendingSession[] = [];
        const now = new Date().getTime();

        for (const sessionID in this.sessionDataStore) {
            if (this.sessionDataStore.hasOwnProperty(sessionID)) {
                const session = this.sessionDataStore[sessionID];
                const howLong = now - session.start;
                if (howLong > PENDING_SESSION_DELTA) {
                    pendingSession.push({
                        action: session.action,
                        pendingSince: howLong,
                        start: session.start,
                        token: session.token,
                        archiveID: session.archiveID,
                    });
                }
            }
        }

        return {
            pendingSession,
            openedSession: openSession.length,
            activePoolConnection,
            connectionLimit: connectionLimitForInstance,
        };
    }

    /**
     * Updates metrics related to pool connections.
     *
     * @returns void
     */
    updatePoolConnectionMetrics(): void {
        const stat = this.getPoolMetrics();
        this.metrics.addValue('activePoolConnection', stat.activePoolConnection, 'Count');

        // [ANOMALY HUNTING] increasing number of connection is a consequence not the real cause
        // if (stat.activePoolConnection > 50) {
        //     // Connection pool too large: forcibly terminate the process
        //     this.logger.getLogger({action: "checkConsistency", module: "gar"}).error("Too many DB connections (" + stat.activePoolConnection + "). Terminating process.");
        //     process.exit(1);
        // }
    }

    /**
     * Runs and logs the SHOW FULL PROCESSLIST MySQL command.
     *
     * @param logger - The logger instance.
     * @returns A promise that resolves when the process list has been logged.
     */
    async runAndLogShowFullProcessList(logger: Logger): Promise<void> {
        await this.mySqlDriverInstance.withConnection(async (connection: PoolConnection) => {
            interface QueryResult {
                rows: unknown[];
                fields: { name: string }[];
            }

            const { rows, fields }: QueryResult = await new Promise((resolve, reject) => {
                connection.query('SHOW FULL PROCESSLIST', (err: MysqlError | null, rows: unknown[], fields: { name: string }[]) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ rows, fields });
                    }
                });
            });

            const fieldNames = fields.map((field) => field.name);
            const processlist = [];

            for (const row of rows) {
                if (!row) {
                    continue;
                }
                const values: Record<string, unknown> = {};
                for (const fieldName of fieldNames) {
                    values[fieldName] = (row as Record<string, unknown>)[fieldName];
                }
                processlist.push(values);
            }

            logger.info('SHOW FULL PROCESSLIST', { processlist });
        });
    }

    /**
     * Checks the consistency of sessions and monitors for stalled sessions.
     *
     * @param metrics - The metrics instance.
     * @param state - Previous state of history of pending sessions count.
     * @param interval - Interval between checks in milliseconds.
     * @returns Updated history of pending sessions count.
     */
    checkConsistency(metrics: Metrics, state: HistoryOfPendingSessionsCount | null, interval: number): HistoryOfPendingSessionsCount {
        const logger = this.logger.getLogger({ action: 'checkConsistency' });
        const sessionStat = this.getSessionStat();
        const now = new Date().getTime();
        const numPendingSessions = sessionStat.pendingSession.length;
        metrics.addValue('numPendingSessions', numPendingSessions, 'Count');

        for (const pendingSession of sessionStat.pendingSession) {
            // log only once, the check is run every "interval" ms
            if (pendingSession.start > now - PENDING_SESSION_DELTA - interval) {
                logger.warn('Stalled session detected', { pendingSession, numPendingSessions });
            }
        }

        return runningDerivative({
            now,
            numPendingSessions,
            state,
            suicide: (avgNewPendingSessionsPerMin) => {
                const delayInSec = Math.round(Math.random() * 60) + 5;
                logger.error(
                    `Too high rate of new stalled sessions detected (${avgNewPendingSessionsPerMin} per min). SUICIDING in ${delayInSec} seconds.`
                );
                this.runAndLogShowFullProcessList(logger).catch((err) => {
                    logger.error('Unexpected error during runAndLogShowFullProcessList', err);
                });
                metrics.addValue('numSuicides', 1, 'Count');
                metrics.putMetricData(); // Push out metrics before suiciding
                setTimeout(suicide, delayInSec * 1000);
            },
            minimumHistoryInSec: MINIMUM_HISTORY_OF_PENDING_SESSIONS_DURATION_IN_SEC,
            historyDurationInSec: HISTORY_OF_PENDING_SESSIONS_DURATION_IN_SEC,
            ratePerMinThreshold: NEW_PENDING_SESSION_RATE_PER_MIN_THRESHOLD,
        });
    }
}

/**
 * Calculates running derivative statistics for monitoring session rates.
 *
 * @param params - Parameters for running derivative calculation.
 * @returns Updated history of pending sessions count.
 */
export function runningDerivative(params: RunningDerivativeParams): HistoryOfPendingSessionsCount {
    const { now, numPendingSessions, state, suicide, minimumHistoryInSec, historyDurationInSec, ratePerMinThreshold } = params;
    let historyOfPendingSessionsCount = state ? state.historyOfPendingSessionsCount : [];
    let newPendingSessionsPerMin: number;

    if (historyOfPendingSessionsCount.length > 0) {
        const { numPendingSessions: oldNumPendingSessions, ts: prevTs } =
            historyOfPendingSessionsCount[historyOfPendingSessionsCount.length - 1];
        newPendingSessionsPerMin = (numPendingSessions - oldNumPendingSessions) / ((now - prevTs) / 1000 / 60);
    } else {
        newPendingSessionsPerMin = 0;
    }

    // Add new datapoint
    historyOfPendingSessionsCount.push({
        ts: now,
        numPendingSessions,
        newPendingSessionsPerMin, // i.e. the derivative
    });

    // Trim older records
    historyOfPendingSessionsCount = historyOfPendingSessionsCount.filter(({ ts }) => (now - ts) / 1000 < historyDurationInSec);

    const avgNewPendingSessionsPerMin =
        historyOfPendingSessionsCount.reduce((acc, { newPendingSessionsPerMin }) => acc + newPendingSessionsPerMin, 0) /
        historyOfPendingSessionsCount.length;
    const totalElapsedTimeInMin = (now - historyOfPendingSessionsCount[0].ts) / 1000 / 60;
    historyOfPendingSessionsCount[historyOfPendingSessionsCount.length - 1].avgNewPendingSessionsPerMin = avgNewPendingSessionsPerMin;

    if (totalElapsedTimeInMin * 60 > minimumHistoryInSec && avgNewPendingSessionsPerMin > ratePerMinThreshold) {
        suicide(avgNewPendingSessionsPerMin);
    }

    return {
        historyOfPendingSessionsCount,
    };
}

/**
 * Exits the process as a last resort to resolve overwhelming pending sessions.
 */
function suicide(): void {
    process.exit(0);
}
