import type { Request, Response } from 'express';
import { broadcastRTCMessage, getUUID } from './utils.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import { type SessionData } from './session-manager.ts';
import type { AppContext } from './app-context.ts';
import onFinished from 'on-finished';

export interface BASRequest extends Request {
    bas: BASRequestContext;
}

export interface AuthContext {
    sessionData?: SessionData;
    platformData?: any;
}

export type BASRequestContext = Awaited<ReturnType<typeof makeBASRequestContext>>;

/**
 * Creates a BASRequestContext which extends the AppContext by adding extra methods and properties
 * related to the current request. This includes session management, request-specific logging,
 * and HTTP response handling capabilities.
 */
function makeBASRequestContext(req: Request, res: Response, appContext: AppContext) {
    const logger = appContext.logger.getLogger({
        reqId: getUUID(),
        sourceIP: req.ip,
        request: {
            method: req.method,
            path: req.path,
            param: req.query,
        },
        userAgent: {
            header: req.headers['user-agent'],
        },
    });
    const actionName = (req.baseUrl + req.path).slice(1);

    let sessionData: SessionData | null = null;

    // CAVEAT: req will become a BASRequest right after returning from this function, however we
    // need to reference req.bas before that.
    _forceAssertBASRequest(req);

    const requestContext = Object.assign({}, appContext, {
        logger,
        actionName,

        // Authentication context for session and platform data
        auth: {} as AuthContext,

        // This is lazy-initialized by the onFinished method.
        // Do not use this property directly (it's exposed for tests). Use onFinished instead.
        _requestFinishedPromise: null as Promise<void> | null,

        addLoggerInfo: (params: { [T: string]: unknown }) => {
            req.bas.logger = req.bas.logger.getLogger(params);
        },
        sendStatusCodeJsonResponse: (statusCode: number, value: unknown) => {
            res.setHeader('Cache-Control', 'no-cache, no-store');
            res.status(statusCode).json(value);
        },

        /**
         * Use onFinished to execute a callback after the request has finished.
         * Keep in mind that your callback might be called before the handler completes (e.g. if the request is aborted).
         *
         * Callbacks are called in the order they are registered.
         */
        onFinished: (callback: () => void | Promise<void>) => {
            if (!req.bas._requestFinishedPromise) {
                req.bas.logger.info(`Request finished promise initialized`);
                req.bas._requestFinishedPromise = new Promise<void>((resolve) => {
                    onFinished(res, (_err) => {
                        req.bas.logger.info(`Request finished`);
                        resolve();
                    });
                });
            }
            req.bas._requestFinishedPromise = req.bas._requestFinishedPromise.then(callback).catch((err) => {
                req.bas.logger.error(`Error in one of the onFinished callbacks`, err as Error);
            });
        },

        /**
         * This function is used to acquire a session for the current request. It checks if a session
         * already exists and creates a new one if it does not.
         *
         * Session is automatically released when the request is finished.
         */
        acquireSession: async ({ token }: { token?: string } = {}) => {
            if (sessionData) {
                return sessionData;
            }
            sessionData = await callWithLegacyCallback<SessionData>((cb) =>
                req.bas.sessionManager.createSession(req.bas.logger, req.bas.actionName, cb)
            );
            if (token) {
                sessionData.token = token;
            }

            req.bas.onFinished(() => req.bas.releaseSession());
            return sessionData;
        },

        /**
         * This function is used to release the session associated with the current request. It should
         * be called to release the session early in the request, when it is no longer needed.
         */
        releaseSession: async () => {
            if (!sessionData) {
                return;
            }
            try {
                const sessionToRelease = sessionData;
                await callWithLegacyCallback((cb) => req.bas.sessionManager.releaseSession(sessionToRelease, cb));
                sessionData = null;
            } catch (err) {
                req.bas.logger.error(`Release request session failed`, err as Error);
            }
        },
        /**
         * This function is used to execute a given action within the context of a session. It ensures
         * that the session is properly released after the action is executed even if an error occurs.
         *
         * This is the proper way to acquire a session that can outlive the request.
         */
        withSession: async <T>(action: (session: SessionData) => Promise<T> | T): Promise<T> => {
            return await req.bas.sessionManager.withSession(req.bas.logger, req.bas.actionName, action);
        },

        verifyAdminCredentials: async () => {
            const isAdmin = await req.bas.checkServerAPICredentials(req);
            if (!isAdmin) {
                req.bas.logger.securityWarningIgnore(`Bad admin credentials`, 'VERIFY_ADMIN_CREDENTIALS_NOT_ADMIN');
            }
            return isAdmin;
        },

        broadcastRTCMessage: (channelId: string, message: Record<string, unknown>) => {
            broadcastRTCMessage({ ...req.bas, channelId, message });
        },
    });

    return requestContext;
}

// CAVEAT: This function is used only by makeBASRequestContext and should not be used anywhere else.
function _forceAssertBASRequest(req: Request): asserts req is BASRequest {}

export function assertBASRequest(req: Request): asserts req is BASRequest {
    if (!isBASRequest(req)) {
        throw new Error('BASRequestContext is not set');
    }
}

export function isBASRequest(req: Request): req is BASRequest {
    return 'bas' in req && !!req.bas;
}

export function augmentRequestObject(req: Request, res: Response, params: AppContext): asserts req is BASRequest {
    if (!isBASRequest(req)) {
        (req as BASRequest).bas = makeBASRequestContext(req, res, params);
    }
}
