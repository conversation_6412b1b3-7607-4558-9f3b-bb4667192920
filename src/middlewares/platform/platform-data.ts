/** @prettier */
import type { NextFunction, Response } from 'express';
import type { BASRequest } from '../../request-context.ts';
import { basMiddleware } from '../../middlewares.ts';

/**
 * Fetches platform data from database
 */
async function fetchPlatformData(req: BASRequest): Promise<any> {
    const sessionData = req.bas.auth.sessionData;
    if (!sessionData) {
        throw new Error('Session data not available');
    }

    const db = sessionData.dbConnector;
    const archiveID = sessionData.user?.ARCHIVE_ID;

    if (!archiveID) {
        throw new Error('Archive ID not available');
    }

    return new Promise((resolve, reject) => {
        db.getPlatformData(archiveID, (obj: any) => {
            if (obj.error) {
                reject(obj);
            } else {
                resolve(obj);
            }
        });
    });
}

/**
 * Creates platform data middleware
 * Fetches and attaches platform data to the request
 */
export function createPlatformDataMiddleware() {
    return basMiddleware(async (req: BASRequest, res: Response, next: NextFunction) => {
        const sessionData = req.bas.auth.sessionData;
        if (!sessionData) {
            return next();
        }

        try {
            const platformData = await fetchPlatformData(req);
            req.bas.auth.platformData = platformData;
            next();
        } catch (error: any) {
            req.bas.logger.error('Failed to fetch platform data', error);
            res.status(500).json({
                error: error.error || 'Failed to fetch platform data',
                code: 500,
            });
        }
    });
}
