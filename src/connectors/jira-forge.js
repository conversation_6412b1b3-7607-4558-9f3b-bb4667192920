//A connector that mimics a file repository like S3

import con from '../constants.ts';
import { JIRAConnector } from "./jira.js";
import * as jwt from './lib/jwt.js';
import superagent from 'superagent';

const CONNECTION_TIMEOUT = 5000;

/**
 * @typedef {Object} GetUserKeyArgs
 * @property {string} platformToken
 * @property {DBConnector} dbConnector
 * @property {Logger} [logger]       // <-- optional
 * @property {any}    [userInfo]     // <-- optional
 * @property {any}    [platformInfo] // <-- optional
 */

export class JIRAForgeConnector extends JIRAConnector {
    constructor(sessionManager, logger, metrics, permalinkImageStorageAdapter, config, serverUtils, w2iAdapter, redisAdapter) {
        super(sessionManager, logger, metrics, permalinkImageStorageAdapter, config, serverUtils, w2iAdapter, redisAdapter);
        this.logger = logger.getLogger({ module: 'jira_forge' });

        this.logger.info("#JIRA FORGE# Connector", { action: "startup" });
    }

    /**
     * @param {GetUserKeyArgs} args
     * @returns {Promise<string>}
     */
    getUserKeyFromPlatformToken = async function ({ platformToken, dbConnector, logger = null, userInfo = null, platformInfo = null }) {
        return `jira_forge-${userInfo?.name}-${platformInfo?.cloudId}`;
    }

    getRole = function (logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, callback) {
        logger = logger.getLogger({ action: "getRole", module: "jira-forge", platformArchiveID: platformArchiveID, platformSiteID: platformSiteID }); //

        const issueIDFromUserInfo = userInfo?.platformInfo?.issueID;
        const apiBaseUrl = userInfo?.platformInfo?.fitClaims?.app?.apiBaseUrl;
        let issueID = issueIDFromUserInfo;

        if (!issueID) {
            logger.error("getRole: Unable to determine issueID for permission check.", { platformSiteID, userInfo });
            return callback({ error: "Unable to determine issueID for permission check." });
        }

        if (userInfo.isAnonymous) { //
            dbConnector.hasPublicShare({ platformKind: "jira-forge", platformSiteID, platformArchiveID }, function (obj) {
                if (obj.error) {
                    logger.error("getRole (Anonymous): Error checking for public share", { error: obj.error });
                    return callback(obj);
                }
                if (obj.hasPublicShare) {
                    if (userInfo.platformInfo) { // Context (like apiBaseUrl) might still be needed
                        logger.info("getRole (Anonymous): Public share found, granting VIEWER role.", { platformArchiveID });
                        callback({ role: con.ROLE_VIEWER, platformToken: userInfo?.platformInfo?.fitClaims?.accessTokens?.user }); //
                    } else {
                        logger.error("getRole (Anonymous): Public share exists but missing platformInfo.", { platformArchiveID });
                        callback({ error: "anonymous user, missing platform info for public share context" });
                    }
                } else {
                    logger.warn("getRole (Anonymous): No public share found for archive.", { platformSiteID, platformArchiveID });
                    callback({ error: "anonymous user, no public share" });
                }
            });
        } else {
            // Authenticated user
            if (!platformToken) {
                logger.error("getRole (Authenticated): platformToken (bearer token) is missing.");
                return callback({ error: "Missing authorization token for permission check." });
            }
            if (!apiBaseUrl) {
                logger.error("getRole (Authenticated): apiBaseUrl is missing from userInfo.platformInfo.");
                return callback({ error: "Missing apiBaseUrl for permission check." });
            }

            const permissionsToQuery = "EDIT_ISSUES,CREATE_ATTACHMENTS,DELETE_OWN_ATTACHMENTS,BROWSE_PROJECTS";
            const permissionsUrl = `${apiBaseUrl}/rest/api/latest/mypermissions?issueId=${issueID}&permissions=${permissionsToQuery}`;

            const loggableUrl = permissionsUrl.replace(apiBaseUrl, "{apiBaseUrl}");
            logger.info("getRole (Authenticated): Checking permissions via URL", { url: loggableUrl });

            superagent
                .get(permissionsUrl)
                .timeout(CONNECTION_TIMEOUT)
                .set({
                    "X-Atlassian-Token": "no-check",
                    "Authorization": "Bearer " + platformToken,
                    'Accept': 'application/json',
                    "x-atlassian-force-account-id": "true" // Added for consistency
                }).end(function (err, res) {
                if (err) {
                    logger.error("getRole (Authenticated): Error calling mypermissions API", {
                        error: err.message,
                        status: err.status, // HTTP status code if available
                        response: err.response?.text, // Full response text
                        url: loggableUrl
                    });
                    return callback({ error: "Failed to retrieve permissions: " + (err.message || `Status ${err.status}`) });
                }

                if (res.statusCode === 200) {
                    const permissionsResponse = res.body;
                    let role = con.ROLE_NO_ACCESS;
                    try {
                        const perms = permissionsResponse.permissions;
                        if (!perms) {
                            logger.error("getRole (Authenticated): 'permissions' field missing in API response.", { response: permissionsResponse });
                            return callback({ error: "Invalid response from permissions API: 'permissions' field missing." });
                        }

                        const hasEditPermissions = perms.EDIT_ISSUES?.havePermission &&
                            perms.CREATE_ATTACHMENTS?.havePermission &&
                            perms.DELETE_OWN_ATTACHMENTS?.havePermission;

                        const hasBrowsePermissions = perms.BROWSE_PROJECTS?.havePermission;

                        if (hasEditPermissions) {
                            role = con.ROLE_ADMIN; //
                        } else if (hasBrowsePermissions) {
                            role = con.ROLE_VIEWER; //
                        } else {
                            role = con.ROLE_NO_ACCESS;
                        }
                        logger.info("getRole (Authenticated): Determined role", { role, issueID });
                        callback({ role: role, platformToken: platformToken });
                    } catch (parseError) {
                        logger.error("getRole (Authenticated): Error parsing permissions from API response", {
                            error: parseError.message,
                            responseBody: res.text // Use res.text if res.body was not proper JSON
                        });
                        callback({ error: "Failed to parse permissions from API response: " + parseError.message });
                    }
                } else if (res.statusCode === 401 || res.statusCode === 403) {
                    logger.warn("getRole (Authenticated): Unauthorized or Forbidden to access permissions.", {
                        statusCode: res.statusCode,
                        issueID
                    });
                    callback({ role: con.ROLE_NO_ACCESS, platformToken: platformToken });
                } else {
                    logger.error("getRole (Authenticated): Unexpected status code from mypermissions API", {
                        statusCode: res.statusCode,
                        responseBody: res.text
                    });
                    callback({ error: `Failed to retrieve permissions, status code: ${res.statusCode}` });
                }
            });
        }
    };

    checkUserInfo = function (logger, dbConnector, userInfo, platformToken, callback) {
        var claims;
        var userAccountId;
        logger = logger.getLogger({
            module: "jira-forge",
            action: "checkUserInfo"
        });

        if (userInfo.isAnonymous) {
            return callback && callback({ success: "ok" });
        }
        try {
            claims = jwt.decode(platformToken, '', true);
        } catch (e) {
            logger.error("Unable to decode JWT, platform token is " + platformToken, { error: e.message });
            return callback({ error: "Invalid platform token: " + e.message });
        }

        if (claims.sub) {
            userAccountId = claims.sub;
            if (userAccountId !== userInfo.name) {
                return callback({ error: `userID is not matching: ${userAccountId} !== ${userInfo.name}` });
            } else {
                return callback({ success: "ok" });
            }
        } else {
            return callback({ error: "Missing user identifier (sub claim) in token" });
        }
    }

    _forgeDeleteAttachment(logger, apiBaseUrl, bearerToken, attachmentID, callback) {
        if (!apiBaseUrl || !bearerToken || !attachmentID) {
            logger.error("ForgeDeleteAttachment: Missing required parameters", { apiBaseUrl: !!apiBaseUrl, bearerToken: !!bearerToken, attachmentID: !!attachmentID });
            return callback({ error: "Missing required parameters for deleting attachment." });
        }
        const url = `${apiBaseUrl}/rest/api/latest/attachment/${attachmentID}`;
        logger.info("ForgeDeleteAttachment: Attempting to delete", { url });

        superagent.delete(url)
            .set({
                "Authorization": "Bearer " + bearerToken,
                "X-Atlassian-Token": "no-check",
                "accept": "application/json",
                "x-atlassian-force-account-id": "true"
            })
            .timeout(CONNECTION_TIMEOUT)
            .end(function (err, res) {
                var result = {}; // Initialize result
                if (err) {
                    logger.error("ForgeDeleteAttachment: Failed to delete resource", { error: err.message, status: err.status, url });
                    result.error = "Failed to delete the resource: " + (err.message || `Status ${err.status}`);
                    if (err.status === 404) {
                        result.error = "Unable to delete the attachment: file not found";
                        result.busy = true;
                    } else if (err.status === 401 || err.status === 403) {
                        result.error = "Forbidden: you don't have permission to remove this attachment.";
                    }
                } else {
                    // No err, means success (e.g. 204 No Content for DELETE)
                    logger.info("ForgeDeleteAttachment: Successfully deleted or action completed", { attachmentID, statusCode: res.statusCode });
                    // For DELETE 204, res.body might be empty.
                    if (res.statusCode === 204) {
                        // Explicit success
                    } else {
                        // If not 204 but still 2xx, log it but consider it success for delete.
                        logger.warn("ForgeDeleteAttachment: Delete action resulted in unexpected success code", {statusCode: res.statusCode});
                    }
                }
                callback && callback(result);
            });
    }

    deleteFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, callback) {
        const _this = this;
        dbConnector.getPlatformInfoByIDs(platformArchiveID, platformSiteID, function (platformData) {
            if (platformData.error) {
                logger.error("DeleteFromPlatform: Error fetching platform data", { error: platformData.error, platformArchiveID });
                return callback(platformData);
            }

            if (platformData.PLATFORM_INFO) {
                try {
                    const platformInfo = JSON.parse(platformData.PLATFORM_INFO);
                    const apiBaseUrl = platformInfo?.fitClaims?.app?.apiBaseUrl;
                    const attachmentID = platformInfo?.attachmentID;

                    if (!apiBaseUrl || !attachmentID) {
                        logger.error("DeleteFromPlatform: Missing apiBaseUrl or attachmentID in platformInfo", { platformInfo });
                        return callback({ error: "DeleteFromPlatform: Essential platform information (apiBaseUrl or attachmentID) is missing." });
                    }
                    _this._forgeDeleteAttachment(logger, apiBaseUrl, authToken, attachmentID, callback);

                } catch (e) {
                    logger.error("DeleteFromPlatform: Error parsing PLATFORM_INFO", { error: e.message, platformInfoString: platformData.PLATFORM_INFO });
                    return callback({ error: "Error processing platform information: " + e.message });
                }
            }
            else {
                logger.warn("DeleteFromPlatform: PLATFORM_INFO not found, unable to delete.", { platformArchiveID });
                callback({ error: "Unable to delete: PLATFORM_INFO not found for the archive." });
            }
        });
    };

    _forgeUploadAttachment(logger, apiBaseUrl, bearerToken, issueID, filename, buffer, callback) {
        if (!apiBaseUrl || !bearerToken || !issueID || !filename || buffer === undefined) {
            logger.error("ForgeUploadAttachment: Missing required parameters", {
                apiBaseUrl: !!apiBaseUrl, bearerToken: !!bearerToken, issueID: !!issueID, filename: !!filename, bufferExists: buffer !== undefined
            });
            return callback({ error: "Missing required parameters for uploading attachment." });
        }

        const uploadUrl = `${apiBaseUrl}/rest/api/latest/issue/${issueID}/attachments`;
        logger.info("ForgeUploadAttachment: Attempting to upload", { url: uploadUrl, filename });

        superagent.post(uploadUrl)
            .set({
                "Authorization": "Bearer " + bearerToken,
                "X-Atlassian-Token": "no-check", // Required by Jira for multipart/form-data
                "Accept": "application/json",   // We expect a JSON response
                "x-atlassian-force-account-id": "true"
            })
            .attach('file', buffer, { filename: filename, contentType: 'application/octet-stream' })
            .field('notifyUsers', 'false')
            .field('comment', 'auto-generated by Balsamiq Wireframes. DO NOT REMOVE')
            .timeout(CONNECTION_TIMEOUT)
            .end(function (err, res) {
                if (err) {
                    logger.error("ForgeUploadAttachment: Failed to upload resource", { error: err.message, status: err.status, response: err.response?.text, url: uploadUrl });
                    return callback({ error: "Failed to upload the resource: " + (err.message || `Status ${err.status}`), url: uploadUrl, code: err.status, responseBody: err.response?.text });
                }

                // No err means 2xx success
                try {
                    const respArray = res.body; // Superagent should parse JSON automatically
                    if (Array.isArray(respArray) && respArray.length > 0) {
                        const obj = respArray[0];
                        const id = obj.id;
                        if (id) {
                            logger.info("ForgeUploadAttachment: Successfully uploaded", { attachmentID: id });
                            callback({ id: id });
                        } else {
                            logger.error("ForgeUploadAttachment: Bad JSON response, ID missing", { body: res.body });
                            callback({ error: "Bad JSON response from Jira, ID missing", url: uploadUrl });
                        }
                    } else {
                        logger.error("ForgeUploadAttachment: Unexpected JSON response structure", { body: res.body });
                        callback({ error: "Unexpected JSON response structure from Jira", url: uploadUrl });
                    }
                } catch (er) { // Catch parsing error if res.body wasn't JSON as expected
                    logger.error("ForgeUploadAttachment: Error parsing JSON response", { error: er.message, responseText: res.text });
                    callback({ error: "Bad JSON response from Jira: " + er.message, url: uploadUrl });
                }
            });
    }

    loadFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, options, platformInfo, callback) {
        const apiBaseUrl = platformInfo?.fitClaims?.app?.apiBaseUrl;
        const _this = this;
        if (!apiBaseUrl) {
            return callback({ error: "apiBaseUrl not found" });
        }
        if (!authToken) {
            return callback({ error: "authToken not found" });
        }
        const attachmentID = platformInfo?.attachmentID;
        if (!attachmentID) {
            return callback({ error: "attachmentID not found in platformInfo" });
        }

        const url = `${apiBaseUrl}/rest/api/latest/attachment/content/${attachmentID}`;
        const loggableUrl = url.replace(apiBaseUrl, "{apiBaseUrl}");
        logger.info("LoadFromPlatform: Attempting to load", { url: loggableUrl });

        superagent.get(url)
            .set({
                "Authorization": "Bearer " + authToken,
                "X-Atlassian-Token": "no-check", // May not be strictly necessary for GET content but good for consistency
                "x-atlassian-force-account-id": "true"
            })
            .buffer(true) // Ensure response body is a Buffer
            .responseType('blob') // Helps superagent handle binary data correctly
            .timeout(CONNECTION_TIMEOUT)
            .end(function (err, res) {
                if (err) {
                    logger.error("LoadFromPlatform: Failed to retrieve resource", { error: err.message, status: err.status, url: loggableUrl });
                    return callback({ error: "Failed to retrieve the resource: " + (err.message || `Status ${err.status}`) });
                }

                // No err means 2xx
                // res.body should be a Buffer due to .buffer(true)
                const newArchiveID = _this.generateArchiveID(false);
                callback({ id: newArchiveID, buffer: res.body, platformInfo });
            });
    }

    save = function (logger, dbConnector, user, archiveID, newPlatformArchiveName, archiveRevision, buffer, dump, options, callback) {
        logger = logger.getLogger({ action: "save", module: "jira_forge", archiveID });
        const _this = this;
        const forceFlush = dump && dump.forceFlush;

        dbConnector.getPlatformData(archiveID, function (platformData) {
            if (platformData.error) {
                logger.error("Save: Error fetching platform data", { error: platformData.error });
                return callback(platformData);
            }

            if (platformData.PLATFORM_INFO) {
                let platformInfo;
                try {
                    platformInfo = JSON.parse(platformData.PLATFORM_INFO);
                } catch (e) {
                    logger.error("Save: Unexpected error parsing PLATFORM_INFO", { error: e.message, platformInfoString: platformData.PLATFORM_INFO });
                    return callback({ error: "Unexpected error: malformed platform parameter (PLATFORM_INFO)" });
                }

                const apiBaseUrl = platformInfo?.fitClaims?.app?.apiBaseUrl;
                const issueID = platformInfo?.issueID;
                const prevAttachmentID = platformInfo?.attachmentID;
                const platformArchiveName = platformData.PLATFORM_ARCHIVE_ID;
                const authToken = user.PLATFORM_TOKEN;

                if (!apiBaseUrl || !issueID) {
                    logger.error("Save: Missing apiBaseUrl or issueID in platformInfo", { platformInfo });
                    return callback({ error: "Save: Essential platform information (apiBaseUrl or issueID) is missing." });
                }

                logger.info("Saving: previous attachmentID is " + prevAttachmentID + (forceFlush ? " forcing flush " : ""), { platformInfo });

                if (forceFlush || !platformInfo.archiveRevisionOnPlatform || platformInfo.archiveRevisionOnPlatform < archiveRevision) {
                    const filename = platformArchiveName + ".bmpr";

                    _this._forgeUploadAttachment(logger, apiBaseUrl, authToken, issueID, filename, buffer, function (uploadObj) {
                        if (uploadObj.error) {
                            logger.error("Save: Error during _forgeUploadAttachment", { errorDetails: uploadObj });
                            return callback(uploadObj); // Propagate the error object
                        }

                        const newAttachmentID = uploadObj.id;
                        platformInfo.attachmentID = newAttachmentID;
                        logger.info("Saving: new attachmentID is " + newAttachmentID);
                        platformInfo.archiveRevisionOnPlatform = archiveRevision;

                        dbConnector.updateArchivePlatformData(platformData.BAS_ARCHIVE_ID, platformData.PLATFORM_KIND, platformData.PLATFORM_SITE_ID, platformData.PLATFORM_ARCHIVE_ID, platformArchiveName, platformInfo, function (updateDbObj) { // Stringify platformInfo
                            if (updateDbObj.error) {
                                logger.error("Save: Error during updateArchivePlatformData", { error: updateDbObj.error });
                                return callback(updateDbObj);
                            }

                            if (prevAttachmentID && prevAttachmentID !== newAttachmentID) {
                                logger.info("Saving: Deleting previous attachmentID: " + prevAttachmentID);
                                _this._forgeDeleteAttachment(logger, apiBaseUrl, authToken, prevAttachmentID, function (deleteObj) {
                                    if (deleteObj.error && deleteObj.error !== "Unable to delete the attachment: file not found") {
                                        logger.warn("Save: Error deleting previous attachment, but continuing", { error: deleteObj.error, prevAttachmentID });
                                    } else if (deleteObj.error) { // File not found case
                                        logger.info("Save: Previous attachment was already deleted or not found.", { prevAttachmentID });
                                    } else { // Success
                                        logger.info("Save: Successfully deleted previous attachment", { prevAttachmentID });
                                    }
                                    callback({ platformArchiveID: platformData.PLATFORM_ARCHIVE_ID });
                                });
                            } else {
                                callback({ platformArchiveID: platformData.PLATFORM_ARCHIVE_ID });
                            }
                        });
                    });
                } else {
                    logger.info("Saving unnecessary: archive revision is not changed or already up to date", { currentRevision: platformInfo.archiveRevisionOnPlatform, newRevision: archiveRevision });
                    callback({ platformArchiveID: platformData.PLATFORM_ARCHIVE_ID, wasAlreadyUpdated: true });
                }
            } else {
                logger.warn("Save: PLATFORM_INFO not found for archiveID. Might be closed or not loaded.", { archiveID });
                callback({ error: "Unable to save: Archive data not found or already closed.", busy: true });
            }
        });
    };
}

export default JIRAForgeConnector;